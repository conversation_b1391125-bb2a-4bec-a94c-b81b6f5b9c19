"use client";

import { useState, useEffect, useCallback } from "react";
import { Cart, CartItem } from "@/types";
import {
  CartService,
  AddToCartData,
  UpdateCartItemData,
  CartSummary,
} from "@/lib/services";

// Hook state interface
interface UseCartState {
  cart: Cart | null;
  loading: boolean;
  error: string | null;
  summary: CartSummary | null;
}

// Main cart hook
export function useCart() {
  const [state, setState] = useState<UseCartState>({
    cart: null,
    loading: true,
    error: null,
    summary: null,
  });

  const fetchCart = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const response = await CartService.getCart();

      const summary = response.data
        ? CartService.calculateCartSummary(response.data, 0, 0.1)
        : null;

      setState({
        cart: response.data || null,
        loading: false,
        error: null,
        summary,
      });
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : "Failed to fetch cart",
      }));
    }
  }, []);

  useEffect(() => {
    fetchCart();
  }, [fetchCart]);

  const addToCart = useCallback(async (data: AddToCartData) => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const response = await CartService.addToCart(data);

      const summary = response.data
        ? CartService.calculateCartSummary(response.data, 0, 0.1)
        : null;

      setState((prev) => ({
        ...prev,
        cart: response.data || null,
        loading: false,
        summary,
      }));

      return response;
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : "Failed to add to cart",
      }));
      throw error;
    }
  }, []);

  const updateCartItem = useCallback(
    async (itemId: string, data: UpdateCartItemData) => {
      try {
        setState((prev) => ({ ...prev, loading: true, error: null }));
        const response = await CartService.updateCartItem(itemId, data);

        const summary = response.data
          ? CartService.calculateCartSummary(response.data, 0, 0.1)
          : null;

        setState((prev) => ({
          ...prev,
          cart: response.data || null,
          loading: false,
          summary,
        }));

        return response;
      } catch (error) {
        setState((prev) => ({
          ...prev,
          loading: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to update cart item",
        }));
        throw error;
      }
    },
    []
  );

  const removeFromCart = useCallback(async (itemId: string) => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const response = await CartService.removeFromCart(itemId);

      const summary = response.data
        ? CartService.calculateCartSummary(response.data, 0, 0.1)
        : null;

      setState((prev) => ({
        ...prev,
        cart: response.data || null,
        loading: false,
        summary,
      }));

      return response;
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error:
          error instanceof Error ? error.message : "Failed to remove from cart",
      }));
      throw error;
    }
  }, []);

  const clearCart = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      await CartService.clearCart();

      setState((prev) => ({
        ...prev,
        cart: null,
        loading: false,
        summary: null,
      }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : "Failed to clear cart",
      }));
      throw error;
    }
  }, []);

  const validateCart = useCallback(() => {
    if (!state.cart) return { isValid: true, invalidItems: [] };
    return CartService.validateCart(state.cart);
  }, [state.cart]);

  const refetch = useCallback(() => {
    fetchCart();
  }, [fetchCart]);

  return {
    ...state,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    validateCart,
    refetch,
    // Computed values
    itemCount: state.cart?.items?.length || 0,
    totalQuantity:
      state.cart?.items?.reduce((sum, item) => sum + item.quantity, 0) || 0,
    isEmpty: !state.cart?.items?.length,
  };
}

// Cart item count hook (lightweight)
export function useCartItemCount() {
  const [count, setCount] = useState(0);
  const [loading, setLoading] = useState(true);

  const fetchCount = useCallback(async () => {
    try {
      setLoading(true);
      const response = await CartService.getCart();
      setCount(
        response.data?.items?.reduce((sum, item) => sum + item.quantity, 0) || 0
      );
    } catch {
      setCount(0);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCount();
  }, [fetchCount]);

  return {
    count,
    loading,
    refetch: fetchCount,
  };
}

// Quick add to cart hook
export function useQuickAddToCart() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const quickAdd = useCallback(
    async (productId: string, quantity: number = 1) => {
      try {
        setLoading(true);
        setError(null);

        const response = await CartService.addToCart({
          productId,
          quantity,
        });

        return response;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to add to cart";
        setError(errorMessage);
        throw error;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  return {
    quickAdd,
    loading,
    error,
  };
}

// Cart validation hook
export function useCartValidation(cart: Cart | null) {
  const [validation, setValidation] = useState<{
    isValid: boolean;
    invalidItems: Array<{ item: CartItem; reason: string }>;
  }>({ isValid: true, invalidItems: [] });

  useEffect(() => {
    if (!cart) {
      setValidation({ isValid: true, invalidItems: [] });
      return;
    }

    const result = CartService.validateCart(cart);
    setValidation(result);
  }, [cart]);

  return validation;
}

// Guest cart merge hook
export function useGuestCartMerge() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const mergeGuestCart = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await CartService.mergeGuestCart();
      return response;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to merge guest cart";
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    mergeGuestCart,
    loading,
    error,
  };
}
