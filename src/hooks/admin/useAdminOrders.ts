import { useCallback, useEffect } from "react";
import { useAdminState } from "@/contexts/AdminContext";
import { useAdminApi } from "./useAdminApi";
import { OrderDto, UpdateOrderDto } from "@/app/dto";

export interface AdminOrdersFilters {
  search?: string;
  status?:
    | "PENDING"
    | "CONFIRMED"
    | "PROCESSING"
    | "SHIPPING"
    | "DELIVERED"
    | "CANCELLED";
  paymentStatus?: "PENDING" | "PAID" | "FAILED" | "REFUNDED";
  dateFrom?: string;
  dateTo?: string;
  customerId?: string;
}

export function useAdminOrders() {
  const {
    data: orders,
    loading,
    error,
    pagination,
    filters,
    updateData,
    updatePagination,
    updateFilters,
    setLoading,
    setError,
    refresh,
  } = useAdminState("admin-orders");

  const { apiCall, isOperationLoading } = useAdminApi();

  const fetchOrders = useCallback(
    async (
      page: number = 1,
      limit: number = 20,
      searchFilters: AdminOrdersFilters = {}
    ) => {
      setLoading(true);
      setError(null);

      try {
        const params = new URLSearchParams({
          page: page.toString(),
          limit: limit.toString(),
          ...Object.fromEntries(
            Object.entries(searchFilters).filter(
              ([_, v]) => v !== undefined && v !== ""
            )
          ),
        });

        const response = await apiCall<OrderDto[]>(
          `/api/admin/orders?${params}`,
          { method: "GET" }
        );

        if (response.success && response.data) {
          updateData(response.data);
          if (response.pagination) {
            updatePagination(response.pagination);
          }
          updateFilters(searchFilters);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch orders");
      }
    },
    [apiCall, updateData, updatePagination, updateFilters, setLoading, setError]
  );

  const getOrderById = useCallback(
    async (id: string): Promise<OrderDto | null> => {
      try {
        const response = await apiCall<OrderDto>(
          `/api/admin/orders/${id}`,
          { method: "GET" },
          `get-order-${id}`
        );

        return response.success && response.data ? response.data : null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to get order"
        );
      }
    },
    [apiCall]
  );

  const updateOrderStatus = useCallback(
    async (
      id: string,
      status: AdminOrdersFilters["status"]
    ): Promise<OrderDto | null> => {
      try {
        const response = await apiCall<OrderDto>(
          `/api/admin/orders/${id}`,
          {
            method: "PUT",
            body: { status },
          },
          `update-order-status-${id}`
        );

        if (response.success && response.data) {
          const updatedOrders = orders.map((order) =>
            order.id === id ? response.data! : order
          );
          updateData(updatedOrders);
          return response.data;
        }
        return null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to update order status"
        );
      }
    },
    [apiCall, orders, updateData]
  );

  const updatePaymentStatus = useCallback(
    async (
      id: string,
      paymentStatus: AdminOrdersFilters["paymentStatus"]
    ): Promise<OrderDto | null> => {
      try {
        const response = await apiCall<OrderDto>(
          `/api/admin/orders/${id}`,
          {
            method: "PUT",
            body: { paymentStatus },
          },
          `update-payment-status-${id}`
        );

        if (response.success && response.data) {
          const updatedOrders = orders.map((order) =>
            order.id === id ? response.data! : order
          );
          updateData(updatedOrders);
          return response.data;
        }
        return null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to update payment status"
        );
      }
    },
    [apiCall, orders, updateData]
  );

  const updateOrder = useCallback(
    async (id: string, orderData: UpdateOrderDto): Promise<OrderDto | null> => {
      try {
        const response = await apiCall<OrderDto>(
          `/api/admin/orders/${id}`,
          {
            method: "PUT",
            body: orderData,
          },
          `update-order-${id}`
        );

        if (response.success && response.data) {
          const updatedOrders = orders.map((order) =>
            order.id === id ? response.data! : order
          );
          updateData(updatedOrders);
          return response.data;
        }
        return null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to update order"
        );
      }
    },
    [apiCall, orders, updateData]
  );

  const cancelOrder = useCallback(
    async (id: string, reason?: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          `/api/admin/orders/${id}/cancel`,
          {
            method: "POST",
            body: { reason },
          },
          `cancel-order-${id}`
        );

        if (response.success) {
          const updatedOrders = orders.map((order) =>
            order.id === id ? { ...order, status: "CANCELLED" as const } : order
          );
          updateData(updatedOrders);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to cancel order"
        );
      }
    },
    [apiCall, orders, updateData]
  );

  const refundOrder = useCallback(
    async (id: string, amount?: number, reason?: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          `/api/admin/orders/${id}/refund`,
          {
            method: "POST",
            body: { amount, reason },
          },
          `refund-order-${id}`
        );

        if (response.success) {
          refresh();
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to refund order"
        );
      }
    },
    [apiCall, refresh]
  );

  const exportOrders = useCallback(
    async (
      filters: AdminOrdersFilters = {},
      format: "csv" | "excel" = "csv"
    ): Promise<Blob | null> => {
      try {
        const params = new URLSearchParams({
          format,
          ...Object.fromEntries(
            Object.entries(filters).filter(
              ([_, v]) => v !== undefined && v !== ""
            )
          ),
        });

        const response = await fetch(`/api/admin/orders/export?${params}`, {
          method: "GET",
          credentials: "include",
        });

        if (response.ok) {
          return await response.blob();
        }
        return null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to export orders"
        );
      }
    },
    []
  );

  const searchOrders = useCallback(
    (searchFilters: AdminOrdersFilters) => {
      fetchOrders(1, pagination.limit, searchFilters);
    },
    [fetchOrders, pagination.limit]
  );

  const changePage = useCallback(
    (page: number) => {
      fetchOrders(page, pagination.limit, filters as AdminOrdersFilters);
    },
    [fetchOrders, pagination.limit, filters]
  );

  const changePageSize = useCallback(
    (limit: number) => {
      fetchOrders(1, limit, filters as AdminOrdersFilters);
    },
    [fetchOrders, filters]
  );

  // Auto-fetch on mount if no data
  useEffect(() => {
    if (orders.length === 0 && !loading && !error) {
      fetchOrders();
    }
  }, [orders.length, loading, error, fetchOrders]);

  return {
    // Data
    orders,
    loading,
    error,
    pagination,
    filters,

    // Actions
    fetchOrders,
    getOrderById,
    updateOrderStatus,
    updatePaymentStatus,
    updateOrder,
    cancelOrder,
    refundOrder,
    exportOrders,
    searchOrders,
    changePage,
    changePageSize,
    refresh,

    // Loading states
    isGettingOrder: (id: string) => isOperationLoading(`get-order-${id}`),
    isUpdatingStatus: (id: string) =>
      isOperationLoading(`update-order-status-${id}`),
    isUpdatingPayment: (id: string) =>
      isOperationLoading(`update-payment-status-${id}`),
    isUpdating: (id: string) => isOperationLoading(`update-order-${id}`),
    isCancelling: (id: string) => isOperationLoading(`cancel-order-${id}`),
    isRefunding: (id: string) => isOperationLoading(`refund-order-${id}`),
  };
}
