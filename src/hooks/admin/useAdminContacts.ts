import { useCallback, useEffect } from "react";
import { useAdminState } from "@/contexts/AdminContext";
import { useAdminApi } from "./useAdminApi";

export interface ContactDto {
  id: string;
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  status: "NEW" | "REPLIED" | "CLOSED";
  createdAt: string;
  updatedAt: string;
  notes?: Array<{
    id: string;
    content: string;
    createdAt: string;
    adminId: string;
    admin: {
      name: string;
      email: string;
    };
  }>;
}

export interface ContactStatsDto {
  total: number;
  new: number;
  replied: number;
  closed: number;
}

export interface AdminContactsFilters {
  search?: string;
  status?: "NEW" | "REPLIED" | "CLOSED";
  dateFrom?: string;
  dateTo?: string;
}

export function useAdminContacts() {
  const {
    data: contacts,
    loading,
    error,
    pagination,
    filters,
    updateData,
    updatePagination,
    updateFilters,
    setLoading,
    setError,
    refresh,
  } = useAdminState("admin-contacts");

  const { apiCall, isOperationLoading } = useAdminApi();

  const fetchContacts = useCallback(
    async (
      page: number = 1,
      limit: number = 20,
      searchFilters: AdminContactsFilters = {}
    ) => {
      setLoading(true);
      setError(null);

      try {
        const params = new URLSearchParams({
          page: page.toString(),
          limit: limit.toString(),
          ...Object.fromEntries(
            Object.entries(searchFilters).filter(
              ([_, v]) => v !== undefined && v !== ""
            )
          ),
        });

        const response = await apiCall<ContactDto[]>(
          `/api/admin/contacts?${params}`,
          { method: "GET" }
        );

        if (response.success && response.data) {
          updateData(response.data);
          if (response.pagination) {
            updatePagination(response.pagination);
          }
          updateFilters(searchFilters);
        }
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to fetch contacts"
        );
      }
    },
    [apiCall, updateData, updatePagination, updateFilters, setLoading, setError]
  );

  const getContactById = useCallback(
    async (id: string): Promise<ContactDto | null> => {
      try {
        const response = await apiCall<ContactDto>(
          `/api/admin/contacts/${id}`,
          { method: "GET" },
          `get-contact-${id}`
        );

        return response.success && response.data ? response.data : null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to get contact"
        );
      }
    },
    [apiCall]
  );

  const updateContactStatus = useCallback(
    async (
      id: string,
      status: AdminContactsFilters["status"]
    ): Promise<ContactDto | null> => {
      try {
        const response = await apiCall<ContactDto>(
          `/api/admin/contacts/${id}`,
          {
            method: "PUT",
            body: { status },
          },
          `update-contact-status-${id}`
        );

        if (response.success && response.data) {
          const updatedContacts = contacts.map((contact) =>
            contact.id === id ? response.data! : contact
          );
          updateData(updatedContacts);
          return response.data;
        }
        return null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to update contact status"
        );
      }
    },
    [apiCall, contacts, updateData]
  );

  const addContactNote = useCallback(
    async (id: string, note: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          `/api/admin/contacts/${id}/notes`,
          {
            method: "POST",
            body: { content: note },
          },
          `add-contact-note-${id}`
        );

        if (response.success) {
          // Refresh the contact to get updated notes
          const updatedContact = await getContactById(id);
          if (updatedContact) {
            const updatedContacts = contacts.map((contact) =>
              contact.id === id ? updatedContact : contact
            );
            updateData(updatedContacts);
          }
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to add note"
        );
      }
    },
    [apiCall, getContactById, contacts, updateData]
  );

  const deleteContact = useCallback(
    async (id: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          `/api/admin/contacts/${id}`,
          { method: "DELETE" },
          `delete-contact-${id}`
        );

        if (response.success) {
          const filteredContacts = contacts.filter(
            (contact) => contact.id !== id
          );
          updateData(filteredContacts);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to delete contact"
        );
      }
    },
    [apiCall, contacts, updateData]
  );

  const getContactStats =
    useCallback(async (): Promise<ContactStatsDto | null> => {
      try {
        const response = await apiCall<ContactStatsDto>(
          "/api/admin/contacts/stats",
          { method: "GET" },
          "get-contact-stats"
        );

        return response.success && response.data ? response.data : null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to get contact stats"
        );
      }
    }, [apiCall]);

  const sendTestEmail = useCallback(
    async (to: string, template: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          "/api/admin/contacts/test-email",
          {
            method: "POST",
            body: { to, template },
          },
          "send-test-email"
        );

        return response.success;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to send test email"
        );
      }
    },
    [apiCall]
  );

  const searchContacts = useCallback(
    (searchFilters: AdminContactsFilters) => {
      fetchContacts(1, pagination.limit, searchFilters);
    },
    [fetchContacts, pagination.limit]
  );

  const changePage = useCallback(
    (page: number) => {
      fetchContacts(page, pagination.limit, filters as AdminContactsFilters);
    },
    [fetchContacts, pagination.limit, filters]
  );

  const changePageSize = useCallback(
    (limit: number) => {
      fetchContacts(1, limit, filters as AdminContactsFilters);
    },
    [fetchContacts, filters]
  );

  // Auto-fetch on mount if no data
  useEffect(() => {
    if (contacts.length === 0 && !loading && !error) {
      fetchContacts();
    }
  }, [contacts.length, loading, error, fetchContacts]);

  return {
    // Data
    contacts,
    loading,
    error,
    pagination,
    filters,

    // Actions
    fetchContacts,
    getContactById,
    updateContactStatus,
    addContactNote,
    deleteContact,
    getContactStats,
    sendTestEmail,
    searchContacts,
    changePage,
    changePageSize,
    refresh,

    // Loading states
    isGettingContact: (id: string) => isOperationLoading(`get-contact-${id}`),
    isUpdatingStatus: (id: string) =>
      isOperationLoading(`update-contact-status-${id}`),
    isAddingNote: (id: string) => isOperationLoading(`add-contact-note-${id}`),
    isDeleting: (id: string) => isOperationLoading(`delete-contact-${id}`),
    isGettingStats: isOperationLoading("get-contact-stats"),
    isSendingTestEmail: isOperationLoading("send-test-email"),
  };
}
