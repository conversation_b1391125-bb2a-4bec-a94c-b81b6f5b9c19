import { useCallback, useEffect } from "react";
import { useAdminState } from "@/contexts/AdminContext";
import { useAdminApi } from "./useAdminApi";

export interface NotificationDto {
  id: string;
  title: string;
  message: string;
  type: "INFO" | "SUCCESS" | "WARNING" | "ERROR";
  status: "UNREAD" | "READ" | "ARCHIVED";
  userId?: string;
  adminId?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  readAt?: string;
  user?: {
    id: string;
    name: string;
    email: string;
  };
  admin?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface NotificationPreferencesDto {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  categories: {
    orders: boolean;
    products: boolean;
    users: boolean;
    system: boolean;
  };
}

export interface AdminNotificationsFilters {
  search?: string;
  type?: "INFO" | "SUCCESS" | "WARNING" | "ERROR";
  status?: "UNREAD" | "READ" | "ARCHIVED";
  dateFrom?: string;
  dateTo?: string;
}

export function useAdminNotifications() {
  const {
    data: notifications,
    loading,
    error,
    pagination,
    filters,
    updateData,
    updatePagination,
    updateFilters,
    setLoading,
    setError,
    refresh,
  } = useAdminState("admin-notifications");

  const { apiCall, isOperationLoading } = useAdminApi();

  const fetchNotifications = useCallback(
    async (
      page: number = 1,
      limit: number = 20,
      searchFilters: AdminNotificationsFilters = {}
    ) => {
      setLoading(true);
      setError(null);

      try {
        const params = new URLSearchParams({
          page: page.toString(),
          limit: limit.toString(),
          ...Object.fromEntries(
            Object.entries(searchFilters).filter(
              ([_, v]) => v !== undefined && v !== ""
            )
          ),
        });

        const response = await apiCall<NotificationDto[]>(
          `/api/admin/notifications?${params}`,
          { method: "GET" }
        );

        if (response.success && response.data) {
          updateData(response.data);
          if (response.pagination) {
            updatePagination(response.pagination);
          }
          updateFilters(searchFilters);
        }
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to fetch notifications"
        );
      }
    },
    [apiCall, updateData, updatePagination, updateFilters, setLoading, setError]
  );

  const getNotificationById = useCallback(
    async (id: string): Promise<NotificationDto | null> => {
      try {
        const response = await apiCall<NotificationDto>(
          `/api/admin/notifications/${id}`,
          { method: "GET" },
          `get-notification-${id}`
        );

        return response.success && response.data ? response.data : null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to get notification"
        );
      }
    },
    [apiCall]
  );

  const markAsRead = useCallback(
    async (id: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          `/api/admin/notifications/${id}`,
          {
            method: "PUT",
            body: { status: "read" },
          },
          `mark-read-${id}`
        );

        if (response.success) {
          const updatedNotifications = notifications.map((notif) =>
            notif.id === id
              ? {
                  ...notif,
                  status: "read" as const,
                  readAt: new Date().toISOString(),
                }
              : notif
          );
          updateData(updatedNotifications);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to mark as read"
        );
      }
    },
    [apiCall, notifications, updateData]
  );

  const markAsUnread = useCallback(
    async (id: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          `/api/admin/notifications/${id}`,
          {
            method: "PUT",
            body: { status: "UNREAD" },
          },
          `mark-unread-${id}`
        );

        if (response.success) {
          const updatedNotifications = notifications.map((notif) =>
            notif.id === id
              ? { ...notif, status: "UNREAD" as const, readAt: undefined }
              : notif
          );
          updateData(updatedNotifications);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to mark as unread"
        );
      }
    },
    [apiCall, notifications, updateData]
  );

  const archiveNotification = useCallback(
    async (id: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          `/api/admin/notifications/${id}`,
          {
            method: "PUT",
            body: { status: "ARCHIVED" },
          },
          `archive-${id}`
        );

        if (response.success) {
          const updatedNotifications = notifications.map((notif) =>
            notif.id === id ? { ...notif, status: "ARCHIVED" as const } : notif
          );
          updateData(updatedNotifications);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to archive notification"
        );
      }
    },
    [apiCall, notifications, updateData]
  );

  const deleteNotification = useCallback(
    async (id: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          `/api/admin/notifications/${id}`,
          { method: "DELETE" },
          `delete-notification-${id}`
        );

        if (response.success) {
          const filteredNotifications = notifications.filter(
            (notif) => notif.id !== id
          );
          updateData(filteredNotifications);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to delete notification"
        );
      }
    },
    [apiCall, notifications, updateData]
  );

  const bulkMarkAsRead = useCallback(
    async (ids: string[]): Promise<boolean> => {
      try {
        const response = await apiCall(
          "/api/admin/notifications/bulk",
          {
            method: "PUT",
            body: { ids, action: "markRead" },
          },
          "bulk-mark-read"
        );

        if (response.success) {
          const updatedNotifications = notifications.map((notif) =>
            ids.includes(notif.id)
              ? {
                  ...notif,
                  status: "read" as const,
                  readAt: new Date().toISOString(),
                }
              : notif
          );
          updateData(updatedNotifications);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to bulk mark as read"
        );
      }
    },
    [apiCall, notifications, updateData]
  );

  const bulkDelete = useCallback(
    async (ids: string[]): Promise<boolean> => {
      try {
        const response = await apiCall(
          "/api/admin/notifications/bulk",
          {
            method: "DELETE",
            body: { ids },
          },
          "bulk-delete"
        );

        if (response.success) {
          const filteredNotifications = notifications.filter(
            (notif) => !ids.includes(notif.id)
          );
          updateData(filteredNotifications);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to bulk delete"
        );
      }
    },
    [apiCall, notifications, updateData]
  );

  const getPreferences =
    useCallback(async (): Promise<NotificationPreferencesDto | null> => {
      try {
        const response = await apiCall<NotificationPreferencesDto>(
          "/api/admin/notifications/preferences",
          { method: "GET" },
          "get-preferences"
        );

        return response.success && response.data ? response.data : null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to get preferences"
        );
      }
    }, [apiCall]);

  const updatePreferences = useCallback(
    async (
      preferences: Partial<NotificationPreferencesDto>
    ): Promise<boolean> => {
      try {
        const response = await apiCall(
          "/api/admin/notifications/preferences",
          {
            method: "PUT",
            body: preferences,
          },
          "update-preferences"
        );

        return response.success;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to update preferences"
        );
      }
    },
    [apiCall]
  );

  const sendEmailNotification = useCallback(
    async (to: string, subject: string, content: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          "/api/admin/notifications/email",
          {
            method: "POST",
            body: { to, subject, content },
          },
          "send-email"
        );

        return response.success;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to send email"
        );
      }
    },
    [apiCall]
  );

  const searchNotifications = useCallback(
    (searchFilters: AdminNotificationsFilters) => {
      fetchNotifications(1, pagination.limit, searchFilters);
    },
    [fetchNotifications, pagination.limit]
  );

  const changePage = useCallback(
    (page: number) => {
      fetchNotifications(
        page,
        pagination.limit,
        filters as AdminNotificationsFilters
      );
    },
    [fetchNotifications, pagination.limit, filters]
  );

  const changePageSize = useCallback(
    (limit: number) => {
      fetchNotifications(1, limit, filters as AdminNotificationsFilters);
    },
    [fetchNotifications, filters]
  );

  // Auto-fetch on mount if no data
  useEffect(() => {
    if (notifications.length === 0 && !loading && !error) {
      fetchNotifications();
    }
  }, [notifications.length, loading, error, fetchNotifications]);

  return {
    // Data
    notifications,
    loading,
    error,
    pagination,
    filters,

    // Actions
    fetchNotifications,
    getNotificationById,
    markAsRead,
    markAsUnread,
    archiveNotification,
    deleteNotification,
    bulkMarkAsRead,
    bulkDelete,
    getPreferences,
    updatePreferences,
    sendEmailNotification,
    searchNotifications,
    changePage,
    changePageSize,
    refresh,

    // Loading states
    isGettingNotification: (id: string) =>
      isOperationLoading(`get-notification-${id}`),
    isMarkingRead: (id: string) => isOperationLoading(`mark-read-${id}`),
    isMarkingUnread: (id: string) => isOperationLoading(`mark-unread-${id}`),
    isArchiving: (id: string) => isOperationLoading(`archive-${id}`),
    isDeleting: (id: string) => isOperationLoading(`delete-notification-${id}`),
    isBulkMarkingRead: isOperationLoading("bulk-mark-read"),
    isBulkDeleting: isOperationLoading("bulk-delete"),
    isGettingPreferences: isOperationLoading("get-preferences"),
    isUpdatingPreferences: isOperationLoading("update-preferences"),
    isSendingEmail: isOperationLoading("send-email"),
  };
}
