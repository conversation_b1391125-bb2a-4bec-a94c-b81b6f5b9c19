/**
 * Product Service
 * Business logic cho Product management
 */

import { Injectable } from "../di-container";
import { BaseService } from "./base.service";
import { CacheService } from "./cache.service";
import { eventService, EVENT_TYPES, ProductEventData } from "./event.service";
import {
  ProductEntity,
  ProductWithRelations,
  CreateProductData,
  UpdateProductData,
  ProductBusinessRules,
} from "../../models/product.model";
import {
  PaginatedResult,
  SearchFilters,
  NotFoundError,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const PRODUCT_SERVICE = Symbol("ProductService");

@Injectable
export class ProductService extends BaseService {
  private productRepository: any;
  private categoryRepository: any;
  private brandRepository: any;
  private cacheService: CacheService;

  constructor(
    productRepository: any,
    categoryRepository: any,
    brandRepository: any,
    cacheService: CacheService
  ) {
    super();
    this.productRepository = productRepository;
    this.categoryRepository = categoryRepository;
    this.brandRepository = brandRepository;
    this.cacheService = cacheService;
  }

  /**
   * Tạo sản phẩm mới
   */
  async createProduct(
    data: CreateProductData,
    createdBy: UserEntity
  ): Promise<ProductEntity> {
    try {
      // Validate permissions
      this.validatePermission(createdBy, "product:create");

      // Validate required fields
      this.validateRequired(data, ["name", "price", "categoryId"]);

      // Business rules validation
      if (!ProductBusinessRules.validatePrice(data.price)) {
        throw new Error("Invalid product price");
      }

      if (
        data.salePrice &&
        !ProductBusinessRules.validateSalePrice(data.price, data.salePrice)
      ) {
        throw new Error("Sale price must be less than regular price");
      }

      // Generate slug if not provided
      const slug = data.slug || ProductBusinessRules.generateSlug(data.name);

      // Check slug uniqueness
      const existingProduct = await this.productRepository.findBySlug(slug);
      if (existingProduct) {
        throw new Error("Product slug already exists");
      }

      // Validate category exists
      if (data.categoryId) {
        const category = await this.categoryRepository.findById(
          data.categoryId
        );
        if (!category) {
          throw new Error("Category not found");
        }
      }

      // Validate brand exists
      if (data.brandId) {
        const brand = await this.brandRepository.findById(data.brandId);
        if (!brand) {
          throw new Error("Brand not found");
        }
      }

      // Create product
      const productData = {
        ...data,
        slug,
        createdBy: createdBy.id,
      };

      const product = await this.productRepository.create(productData);

      // Log activity
      await this.logActivity("product:created", createdBy.id, {
        productId: product.id,
        productName: product.name,
      });

      // Emit product created event
      await eventService.emit<ProductEventData>(EVENT_TYPES.PRODUCT_CREATED, {
        productId: product.id,
        productName: product.name,
        userId: createdBy.id,
      });

      // Invalidate cache
      await this.cacheService.invalidateByTags([CacheService.TAGS.PRODUCTS]);

      return product;
    } catch (error) {
      this.handleError(error, "ProductService.createProduct");
    }
  }

  /**
   * Cập nhật sản phẩm
   */
  async updateProduct(
    id: string,
    data: UpdateProductData,
    updatedBy: UserEntity
  ): Promise<ProductEntity> {
    try {
      // Validate permissions
      this.validatePermission(updatedBy, "product:update");

      // Get existing product
      const existingProduct = await this.productRepository.findById(id);
      if (!existingProduct) {
        throw new Error("Product not found");
      }

      // Business rules validation
      if (data.price && !ProductBusinessRules.validatePrice(data.price)) {
        throw new Error("Invalid product price");
      }

      if (
        data.salePrice &&
        !ProductBusinessRules.validateSalePrice(
          data.price || existingProduct.price,
          data.salePrice
        )
      ) {
        throw new Error("Sale price must be less than regular price");
      }

      // Handle slug update
      if (data.name && data.name !== existingProduct.name) {
        const newSlug = ProductBusinessRules.generateSlug(data.name);
        if (newSlug !== existingProduct.slug) {
          const existingSlug = await this.productRepository.findBySlug(newSlug);
          if (existingSlug && existingSlug.id !== id) {
            throw new Error("Product slug already exists");
          }
          data.slug = newSlug;
        }
      }

      // Validate category if changed
      if (data.categoryId && data.categoryId !== existingProduct.categoryId) {
        const category = await this.categoryRepository.findById(
          data.categoryId
        );
        if (!category) {
          throw new Error("Category not found");
        }
      }

      // Validate brand if changed
      if (data.brandId && data.brandId !== existingProduct.brandId) {
        const brand = await this.brandRepository.findById(data.brandId);
        if (!brand) {
          throw new Error("Brand not found");
        }
      }

      // Update product
      const updateData = {
        ...data,
        updatedBy: updatedBy.id,
        updatedAt: new Date(),
      };

      const product = await this.productRepository.update(id, updateData);

      // Log activity
      await this.logActivity("product:updated", updatedBy.id, {
        productId: product.id,
        productName: product.name,
        changes: Object.keys(data),
      });

      return product;
    } catch (error) {
      this.handleError(error, "ProductService.updateProduct");
    }
  }

  /**
   * Xóa sản phẩm
   */
  async deleteProduct(id: string, deletedBy: UserEntity): Promise<void> {
    try {
      // Validate permissions
      this.validatePermission(deletedBy, "product:delete");

      // Get existing product
      const existingProduct = await this.productRepository.findById(id);
      if (!existingProduct) {
        throw new Error("Product not found");
      }

      // Check if product can be deleted
      if (!ProductBusinessRules.canDelete(existingProduct)) {
        throw new Error("Product cannot be deleted");
      }

      // Soft delete
      await this.productRepository.softDelete(id, deletedBy.id);

      // Log activity
      await this.logActivity("product:deleted", deletedBy.id, {
        productId: id,
        productName: existingProduct.name,
      });
    } catch (error) {
      this.handleError(error, "ProductService.deleteProduct");
    }
  }

  /**
   * Lấy sản phẩm theo ID (with caching)
   */
  async getProductById(
    id: string,
    includeRelations = false
  ): Promise<ProductEntity | ProductWithRelations | null> {
    try {
      const cacheKey = CacheService.generateKey(
        "product",
        id,
        includeRelations ? "with-relations" : "basic"
      );

      return await this.cacheService.getOrSet(
        cacheKey,
        async () => {
          if (includeRelations) {
            return await this.productRepository.findByIdWithRelations(id);
          }
          return await this.productRepository.findById(id);
        },
        {
          ttl: CacheService.TTL.MEDIUM,
          tags: [CacheService.TAGS.PRODUCTS],
        }
      );
    } catch (error) {
      this.handleError(error, "ProductService.getProductById");
      return null;
    }
  }

  /**
   * Lấy sản phẩm theo slug
   */
  async getProductBySlug(
    slug: string,
    includeRelations = false
  ): Promise<ProductEntity | ProductWithRelations | null> {
    try {
      if (includeRelations) {
        return await this.productRepository.findBySlugWithRelations(slug);
      }
      return await this.productRepository.findBySlug(slug);
    } catch (error) {
      this.handleError(error, "ProductService.getProductBySlug");
    }
  }

  /**
   * Tìm kiếm sản phẩm
   */
  async searchProducts(
    filters: SearchFilters
  ): Promise<PaginatedResult<ProductEntity>> {
    try {
      return await this.productRepository.search(filters);
    } catch (error) {
      this.handleError(error, "ProductService.searchProducts");
    }
  }

  /**
   * Lấy sản phẩm nổi bật
   */
  async getFeaturedProducts(limit = 10): Promise<ProductEntity[]> {
    try {
      return await this.productRepository.findFeatured(limit);
    } catch (error) {
      this.handleError(error, "ProductService.getFeaturedProducts");
    }
  }

  /**
   * Lấy sản phẩm theo danh mục
   */
  async getProductsByCategory(
    categoryId: string,
    filters?: SearchFilters
  ): Promise<PaginatedResult<ProductEntity>> {
    try {
      return await this.productRepository.findByCategory(categoryId, filters);
    } catch (error) {
      this.handleError(error, "ProductService.getProductsByCategory");
    }
  }

  /**
   * Cập nhật stock sản phẩm
   */
  async updateStock(
    id: string,
    quantity: number,
    updatedBy: UserEntity
  ): Promise<ProductEntity> {
    try {
      // Validate permissions
      this.validatePermission(updatedBy, "product:update_stock");

      // Get existing product
      const existingProduct = await this.productRepository.findById(id);
      if (!existingProduct) {
        throw new Error("Product not found");
      }

      // Validate stock quantity
      if (quantity < 0) {
        throw new Error("Stock quantity cannot be negative");
      }

      // Update stock
      const product = await this.productRepository.updateStock(id, quantity);

      // Log activity
      await this.logActivity("product:stock_updated", updatedBy.id, {
        productId: id,
        oldStock: existingProduct.stock,
        newStock: quantity,
      });

      return product;
    } catch (error) {
      this.handleError(error, "ProductService.updateStock");
    }
  }

  /**
   * Get related products based on category, brand, price, and tags
   */
  /**
   * Get related products based on category, brand, price, and tags
   */
  async getRelatedProducts(
    productId: string,
    limit: number = 4
  ): Promise<ProductEntity[]> {
    return this.executeWithErrorHandling(async () => {
      // Get the current product to find related products
      const currentProduct = await this.productRepository.findById(productId);
      if (!currentProduct) {
        throw new NotFoundError("Product", productId);
      }

      // Use repository method to get related products
      const relatedProducts = await this.productRepository.getRelatedProducts(
        productId,
        currentProduct.categoryId,
        limit
      );

      return relatedProducts as unknown as ProductEntity[];
    }, "getRelatedProducts");
  }

  /**
   * Tăng view count
   */
  async incrementViewCount(id: string): Promise<void> {
    try {
      await this.productRepository.incrementViewCount(id);
    } catch (error) {
      // Don't throw error for view count increment
      console.error("Failed to increment view count:", error);
    }
  }
}
