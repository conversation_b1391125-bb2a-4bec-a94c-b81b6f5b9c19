/**
 * Setting Service
 * Business logic cho System Settings management
 */

import { BaseService } from "./base.service";
import { Injectable } from "../di-container";
import { SettingRepository } from "../repositories";
import {
  SettingEntity,
  CreateSettingData,
  UpdateSettingData,
  SettingBusinessRules,
  SettingType,
} from "../../models/setting.model";
import {
  PaginatedResult,
  SearchFilters,
  NotFoundError,
  ValidationError,
  ConflictError,
  ForbiddenError,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const SETTING_SERVICE = Symbol("SettingService");

@Injectable
export class SettingService extends BaseService {
  private settingRepository: SettingRepository;

  constructor(settingRepository: SettingRepository) {
    super();
    this.settingRepository = settingRepository;
  }

  /**
   * Tạo setting mới
   */
  async createSetting(
    data: CreateSettingData,
    createdBy: UserEntity
  ): Promise<SettingEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(createdBy)) {
        throw new ForbiddenError("Only admins can create settings");
      }

      // Validate input
      this.validateRequired(data, ["key", "value", "type"]);

      // Validate setting data
      const validation = SettingBusinessRules.validateSetting(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      // Check key uniqueness
      const existingSetting = await this.settingRepository.findByKey(data.key);
      if (existingSetting) {
        throw new ConflictError(
          `Setting with key '${data.key}' already exists`
        );
      }

      // Validate value based on type
      if (!SettingBusinessRules.validateValue(data.value, data.type)) {
        throw new ValidationError(`Invalid value for type '${data.type}'`);
      }

      // Create setting
      const settingData = {
        ...data,
        isPublic: data.isPublic ?? false,
      };

      const setting = (await this.settingRepository.create(
        settingData
      )) as unknown as SettingEntity;

      // Log activity
      await this.logActivity("SETTING_CREATED", createdBy.id, {
        settingId: setting.id,
        key: setting.key,
        type: setting.type,
      });

      return setting;
    }, "createSetting");
  }

  /**
   * Lấy setting theo ID
   */
  async getSettingById(
    id: string,
    requestedBy?: UserEntity
  ): Promise<SettingEntity> {
    return this.executeWithErrorHandling(async () => {
      const setting = await this.settingRepository.findById(id);
      if (!setting) {
        throw new NotFoundError("Setting", id);
      }

      // Check if setting is public or user is admin (simplified)
      // if (!setting.isPublic && (!requestedBy || !this.isAdmin(requestedBy))) {
      //   throw new ForbiddenError("Cannot access private setting");
      // }

      return setting as unknown as SettingEntity;
    }, "getSettingById");
  }

  /**
   * Lấy setting theo key
   */
  async getSettingByKey(
    key: string,
    requestedBy?: UserEntity
  ): Promise<SettingEntity | null> {
    return this.executeWithErrorHandling(async () => {
      const setting = await this.settingRepository.findByKey(key);
      if (!setting) {
        return null;
      }

      // Check if setting is public or user is admin (simplified)
      // if (!setting.isPublic && (!requestedBy || !this.isAdmin(requestedBy))) {
      //   throw new ForbiddenError("Cannot access private setting");
      // }

      return setting as unknown as SettingEntity;
    }, "getSettingByKey");
  }

  /**
   * Lấy setting value theo key
   */
  async getSettingValue<T = any>(
    key: string,
    defaultValue?: T,
    requestedBy?: UserEntity
  ): Promise<T> {
    return this.executeWithErrorHandling(async () => {
      const setting = await this.getSettingByKey(key, requestedBy);
      if (!setting) {
        return defaultValue as T;
      }

      return SettingBusinessRules.parseValue(setting.value, setting.type) as T;
    }, "getSettingValue");
  }

  /**
   * Cập nhật setting
   */
  async updateSetting(
    id: string,
    data: UpdateSettingData,
    updatedBy: UserEntity
  ): Promise<SettingEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Only admins can update settings");
      }

      // Check if setting exists
      const existingSetting = await this.settingRepository.findById(id);
      if (!existingSetting) {
        throw new NotFoundError("Setting", id);
      }

      // Validate key if provided
      if (data.key && data.key !== existingSetting.key) {
        const settingWithKey = await this.settingRepository.findByKey(data.key);
        if (settingWithKey) {
          throw new ConflictError(
            `Setting with key '${data.key}' already exists`
          );
        }
      }

      // Validate value if provided
      const type = data.type || (existingSetting.type as any);
      if (
        data.value &&
        !SettingBusinessRules.validateValue(data.value, type as SettingType)
      ) {
        throw new ValidationError(`Invalid value for type '${type}'`);
      }

      // Update setting
      const updatedSetting = (await this.settingRepository.update(
        id,
        data
      )) as unknown as SettingEntity;

      // Log activity
      await this.logActivity("SETTING_UPDATED", updatedBy.id, {
        settingId: id,
        key: updatedSetting.key,
        changes: Object.keys(data),
      });

      return updatedSetting;
    }, "updateSetting");
  }

  /**
   * Cập nhật setting value theo key
   */
  async updateSettingValue(
    key: string,
    value: any,
    updatedBy: UserEntity
  ): Promise<SettingEntity> {
    return this.executeWithErrorHandling(async () => {
      const setting = await this.settingRepository.findByKey(key);
      if (!setting) {
        throw new NotFoundError("Setting", key);
      }

      return await this.updateSetting(setting.id, { value }, updatedBy);
    }, "updateSettingValue");
  }

  /**
   * Xóa setting
   */
  async deleteSetting(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(deletedBy)) {
        throw new ForbiddenError("Only admins can delete settings");
      }

      // Check if setting exists
      const setting = await this.settingRepository.findById(id);
      if (!setting) {
        throw new NotFoundError("Setting", id);
      }

      // Check if setting can be deleted
      if (
        !SettingBusinessRules.canDelete(setting as unknown as SettingEntity)
      ) {
        throw new ValidationError("Cannot delete system required setting");
      }

      // Delete setting
      await this.settingRepository.delete(id);

      // Log activity
      await this.logActivity("SETTING_DELETED", deletedBy.id, {
        settingId: id,
        key: setting.key,
      });
    }, "deleteSetting");
  }

  /**
   * Lấy danh sách settings
   */
  async getSettings(
    filters: SearchFilters & {
      category?: string;
      type?: string;
      isPublic?: boolean;
    } = {},
    requestedBy?: UserEntity
  ): Promise<PaginatedResult<SettingEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = {};

      // Non-admin users can only see public settings
      if (!requestedBy || !this.isAdmin(requestedBy)) {
        searchConditions.isPublic = true;
      } else if (filters.isPublic !== undefined) {
        searchConditions.isPublic = filters.isPublic;
      }

      if (filters.search) {
        const searchQuery = this.sanitizeSearchQuery(filters.search);
        searchConditions.OR = [
          { key: { contains: searchQuery, mode: "insensitive" } },
          { description: { contains: searchQuery, mode: "insensitive" } },
        ];
      }

      if (filters.category) {
        searchConditions.category = filters.category;
      }

      if (filters.type) {
        searchConditions.type = filters.type;
      }

      // Get settings with pagination
      const result = await this.settingRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || "category"]: filters.sortOrder || "asc",
        },
      });

      return {
        data: result.data.map((setting) => setting as unknown as SettingEntity),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      };
    }, "getSettings");
  }

  /**
   * Lấy public settings
   */
  async getPublicSettings(): Promise<Record<string, any>> {
    return this.executeWithErrorHandling(async () => {
      // Get all settings and filter public ones
      const settings = await this.settingRepository.findMany({
        where: {
          // isPublic: true // Remove if not in schema
        },
      });

      const result: Record<string, any> = {};
      settings.forEach((setting: any) => {
        result[setting.key] = SettingBusinessRules.parseValue(
          setting.value,
          setting.type
        );
      });

      return result;
    }, "getPublicSettings");
  }

  /**
   * Lấy settings theo category
   */
  async getSettingsByCategory(
    category: string,
    requestedBy?: UserEntity
  ): Promise<SettingEntity[]> {
    return this.executeWithErrorHandling(async () => {
      const settings = await this.settingRepository.findMany({
        where: { category },
      });

      // Filter out private settings for non-admin users (simplified)
      const filteredSettings = settings.filter((setting: any) => {
        return true; // For now, return all settings
        // return setting.isPublic || (requestedBy && this.isAdmin(requestedBy));
      });

      return filteredSettings.map(
        (setting: any) => setting as unknown as SettingEntity
      );
    }, "getSettingsByCategory");
  }

  /**
   * Bulk update settings
   */
  async bulkUpdateSettings(
    updates: { key: string; value: any }[],
    updatedBy: UserEntity
  ): Promise<{
    success: string[];
    failed: { key: string; reason: string }[];
  }> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Only admins can bulk update settings");
      }

      const success: string[] = [];
      const failed: { key: string; reason: string }[] = [];

      for (const update of updates) {
        try {
          await this.updateSettingValue(update.key, update.value, updatedBy);
          success.push(update.key);
        } catch (error) {
          failed.push({
            key: update.key,
            reason: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }

      // Log activity
      await this.logActivity("SETTINGS_BULK_UPDATED", updatedBy.id, {
        successCount: success.length,
        failedCount: failed.length,
      });

      return { success, failed };
    }, "bulkUpdateSettings");
  }

  /**
   * Reset setting to default value
   */
  async resetSetting(
    id: string,
    updatedBy: UserEntity
  ): Promise<SettingEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Only admins can reset settings");
      }

      // Check if setting exists
      const setting = await this.settingRepository.findById(id);
      if (!setting) {
        throw new NotFoundError("Setting", id);
      }

      // Get default value
      const defaultValue = SettingBusinessRules.getDefaultValue(
        setting.type as SettingType
      );

      // Update setting with default value
      const updatedSetting = await this.updateSetting(
        id,
        { value: defaultValue },
        updatedBy
      );

      // Log activity
      await this.logActivity("SETTING_RESET", updatedBy.id, {
        settingId: id,
        key: setting.key,
      });

      return updatedSetting;
    }, "resetSetting");
  }

  /**
   * Helper method to check if user is admin
   */
  private isAdmin(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN"].includes(user.role);
  }
}
