/**
 * Page Service
 * Business logic cho Static Page management
 */

import { BaseService } from "./base.service";
import { Injectable } from "../di-container";
import { PageRepository } from "../repositories";
import {
  PageEntity,
  CreatePageData,
  UpdatePageData,
  PageBusinessRules,
} from "../../models/page.model";
import {
  PaginatedResult,
  SearchFilters,
  NotFoundError,
  ValidationError,
  ConflictError,
  ForbiddenError,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";
import { PageStatus } from "@prisma/client";

// Service identifier
export const PAGE_SERVICE = Symbol("PageService");

@Injectable
export class PageService extends BaseService {
  private pageRepository: PageRepository;

  constructor(pageRepository: PageRepository) {
    super();
    this.pageRepository = pageRepository;
  }

  /**
   * Tạo page mới
   */
  async createPage(
    data: CreatePageData,
    createdBy: UserEntity
  ): Promise<PageEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(createdBy)) {
        throw new ForbiddenError("Only admins can create pages");
      }

      // Validate input
      this.validateRequired(data, ["title", "content"]);

      // Validate page data
      const validation = PageBusinessRules.validatePage(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      // Check slug uniqueness if provided
      if (data.slug) {
        const existingPage = await this.pageRepository.findBySlug(data.slug);
        if (existingPage) {
          throw new ConflictError(
            `Page with slug '${data.slug}' already exists`
          );
        }
      }

      // Generate slug if not provided
      const slug = data.slug || PageBusinessRules.generateSlug(data.title);

      // Create page
      const pageData = {
        ...data,
        slug,
        status: data.status || PageStatus.DRAFT,
      };

      const page = (await this.pageRepository.create(
        pageData as any
      )) as unknown as PageEntity;

      // Log activity
      await this.logActivity("PAGE_CREATED", createdBy.id, {
        pageId: page.id,
        title: page.title,
        slug: page.slug,
      });

      return page;
    }, "createPage");
  }

  /**
   * Lấy page theo ID
   */
  async getPageById(id: string, requestedBy?: UserEntity): Promise<PageEntity> {
    return this.executeWithErrorHandling(async () => {
      const page = await this.pageRepository.findById(id);
      if (!page) {
        throw new NotFoundError("Page", id);
      }

      // Check if page is published or user has permission
      if (
        page.status !== "PUBLISHED" &&
        (!requestedBy || !this.isAdmin(requestedBy))
      ) {
        throw new ForbiddenError("Cannot access unpublished page");
      }

      return page as unknown as PageEntity;
    }, "getPageById");
  }

  /**
   * Lấy page theo slug
   */
  async getPageBySlug(
    slug: string,
    requestedBy?: UserEntity
  ): Promise<PageEntity> {
    return this.executeWithErrorHandling(async () => {
      const page = await this.pageRepository.findBySlug(slug);
      if (!page) {
        throw new NotFoundError("Page", slug);
      }

      // Check if page is published or user has permission
      if (
        page.status !== "PUBLISHED" &&
        (!requestedBy || !this.isAdmin(requestedBy))
      ) {
        throw new ForbiddenError("Cannot access unpublished page");
      }

      return page as unknown as PageEntity;
    }, "getPageBySlug");
  }

  /**
   * Cập nhật page
   */
  async updatePage(
    id: string,
    data: UpdatePageData,
    updatedBy: UserEntity
  ): Promise<PageEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Only admins can update pages");
      }

      // Check if page exists
      const existingPage = await this.pageRepository.findById(id);
      if (!existingPage) {
        throw new NotFoundError("Page", id);
      }

      // Validate slug if provided
      if (data.slug && data.slug !== existingPage.slug) {
        const pageWithSlug = await this.pageRepository.findBySlug(data.slug);
        if (pageWithSlug) {
          throw new ConflictError(
            `Page with slug '${data.slug}' already exists`
          );
        }
      }

      // Update page
      const updatedPage = (await this.pageRepository.update(
        id,
        data as any
      )) as unknown as PageEntity;

      // Log activity
      await this.logActivity("PAGE_UPDATED", updatedBy.id, {
        pageId: id,
        changes: Object.keys(data),
      });

      return updatedPage;
    }, "updatePage");
  }

  /**
   * Xóa page
   */
  async deletePage(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(deletedBy)) {
        throw new ForbiddenError("Only admins can delete pages");
      }

      // Check if page exists
      const page = await this.pageRepository.findById(id);
      if (!page) {
        throw new NotFoundError("Page", id);
      }

      // Check if page can be deleted
      if (!PageBusinessRules.canDelete(page as unknown as PageEntity)) {
        throw new ValidationError("Cannot delete system required page");
      }

      // Delete page
      await this.pageRepository.delete(id);

      // Log activity
      await this.logActivity("PAGE_DELETED", deletedBy.id, {
        pageId: id,
        title: page.title,
        slug: page.slug,
      });
    }, "deletePage");
  }

  /**
   * Lấy danh sách pages
   */
  async getPages(
    filters: SearchFilters & {
      status?: string;
      template?: string;
    } = {},
    requestedBy?: UserEntity
  ): Promise<PaginatedResult<PageEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = {};

      // Non-admin users can only see published pages
      if (!requestedBy || !this.isAdmin(requestedBy)) {
        searchConditions.status = "PUBLISHED";
      } else if (filters.status) {
        searchConditions.status = filters.status;
      }

      if (filters.template) {
        searchConditions.template = filters.template;
      }

      if (filters.search) {
        const searchQuery = this.sanitizeSearchQuery(filters.search);
        searchConditions.OR = [
          { title: { contains: searchQuery, mode: "insensitive" } },
          { content: { contains: searchQuery, mode: "insensitive" } },
          { slug: { contains: searchQuery, mode: "insensitive" } },
        ];
      }

      // Get pages with pagination
      const result = await this.pageRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || "title"]: filters.sortOrder || "asc",
        },
      });

      return {
        data: result.data.map((page) => page as unknown as PageEntity),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      };
    }, "getPages");
  }

  /**
   * Publish page
   */
  async publishPage(id: string, publishedBy: UserEntity): Promise<PageEntity> {
    return this.executeWithErrorHandling(async () => {
      const page = await this.updatePage(
        id,
        { status: PageStatus.PUBLISHED },
        publishedBy
      );

      // Log activity
      await this.logActivity("PAGE_PUBLISHED", publishedBy.id, {
        pageId: id,
      });

      return page;
    }, "publishPage");
  }

  /**
   * Unpublish page
   */
  async unpublishPage(
    id: string,
    unpublishedBy: UserEntity
  ): Promise<PageEntity> {
    return this.executeWithErrorHandling(async () => {
      const page = await this.updatePage(
        id,
        { status: PageStatus.DRAFT },
        unpublishedBy
      );

      // Log activity
      await this.logActivity("PAGE_UNPUBLISHED", unpublishedBy.id, {
        pageId: id,
      });

      return page;
    }, "unpublishPage");
  }

  /**
   * Get page by template
   */
  async getPagesByTemplate(
    template: string,
    requestedBy?: UserEntity
  ): Promise<PageEntity[]> {
    return this.executeWithErrorHandling(async () => {
      const searchConditions: any = { template };

      // Non-admin users can only see published pages
      if (!requestedBy || !this.isAdmin(requestedBy)) {
        searchConditions.status = "PUBLISHED";
      }

      const pages = await this.pageRepository.findMany({
        where: searchConditions,
        orderBy: { title: "asc" },
      });

      return pages.map((page) => page as unknown as PageEntity);
    }, "getPagesByTemplate");
  }

  /**
   * Duplicate page
   */
  async duplicatePage(
    id: string,
    newTitle: string,
    createdBy: UserEntity
  ): Promise<PageEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(createdBy)) {
        throw new ForbiddenError("Only admins can duplicate pages");
      }

      // Get original page
      const originalPage = await this.pageRepository.findById(id);
      if (!originalPage) {
        throw new NotFoundError("Page", id);
      }

      // Create duplicate data
      const duplicateData: CreatePageData = {
        title: newTitle,
        slug: PageBusinessRules.generateSlug(newTitle),
        content: originalPage.content,
        excerpt: originalPage.excerpt || undefined,
        status: PageStatus.DRAFT,
      };

      // Create duplicate page
      const duplicatePage = await this.createPage(duplicateData, createdBy);

      // Log activity
      await this.logActivity("PAGE_DUPLICATED", createdBy.id, {
        originalPageId: id,
        duplicatePageId: duplicatePage.id,
        newTitle,
      });

      return duplicatePage;
    }, "duplicatePage");
  }

  /**
   * Get navigation pages
   */
  async getNavigationPages(): Promise<PageEntity[]> {
    return this.executeWithErrorHandling(async () => {
      const pages = await this.pageRepository.findMany({
        where: {
          status: "PUBLISHED",
          showInNavigation: true,
        },
        orderBy: { sortOrder: "asc" },
      });

      return pages.map((page) => page as unknown as PageEntity);
    }, "getNavigationPages");
  }

  /**
   * Update page sort order
   */
  async updateSortOrder(
    id: string,
    sortOrder: number,
    updatedBy: UserEntity
  ): Promise<PageEntity> {
    return this.executeWithErrorHandling(async () => {
      const page = await this.updatePage(id, { sortOrder }, updatedBy);

      // Log activity
      await this.logActivity("PAGE_SORT_ORDER_UPDATED", updatedBy.id, {
        pageId: id,
        sortOrder,
      });

      return page;
    }, "updateSortOrder");
  }

  /**
   * Helper method to check if user is admin
   */
  private isAdmin(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN"].includes(user.role);
  }
}
