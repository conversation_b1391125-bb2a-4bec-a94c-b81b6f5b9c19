/**
 * Menu Service
 * Business logic cho Menu management
 */

import { BaseService } from "./base.service";
import { Injectable } from "../di-container";
import { MenuRepository } from "../repositories";
import {
  MenuEntity,
  CreateMenuData,
  UpdateMenuData,
  MenuBusinessRules,
  MenuTreeNode,
} from "../../models/menu.model";
import {
  PaginatedResult,
  SearchFilters,
  NotFoundError,
  ValidationError,
  ConflictError,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const MENU_SERVICE = Symbol("MenuService");

@Injectable
export class MenuService extends BaseService {
  private menuRepository: MenuRepository;

  constructor(menuRepository: MenuRepository) {
    super();
    this.menuRepository = menuRepository;
  }

  /**
   * Tạo menu mới
   */
  async createMenu(
    data: CreateMenuData,
    createdBy: UserEntity
  ): Promise<MenuEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(createdBy)) {
        throw new ValidationError("Only admins can create menus");
      }

      // Validate input
      this.validateRequired(data, ["name", "location"]);

      // Validate menu data
      const validation = MenuBusinessRules.validateMenu(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      // Check if menu with same location already exists
      const existingMenu = await this.menuRepository.findByLocation(
        data.location
      );
      if (existingMenu) {
        throw new ConflictError(
          `Menu for location '${data.location}' already exists`
        );
      }

      // Create menu
      const menuData = {
        ...data,
        status: data.status || "ACTIVE",
      };

      const menu = (await this.menuRepository.create(
        menuData
      )) as unknown as MenuEntity;

      // Log activity
      await this.logActivity("MENU_CREATED", createdBy.id, {
        menuId: menu.id,
        menuName: menu.name,
        location: menu.location,
      });

      return menu;
    }, "createMenu");
  }

  /**
   * Lấy menu theo ID
   */
  async getMenuById(id: string): Promise<MenuEntity> {
    return this.executeWithErrorHandling(async () => {
      const menu = await this.menuRepository.findById(id);
      if (!menu) {
        throw new NotFoundError("Menu", id);
      }
      return menu as unknown as MenuEntity;
    }, "getMenuById");
  }

  /**
   * Lấy menu theo location
   */
  async getMenuByLocation(location: string): Promise<MenuEntity | null> {
    return this.executeWithErrorHandling(async () => {
      const menu = await this.menuRepository.findByLocation(location);
      return menu ? (menu as unknown as MenuEntity) : null;
    }, "getMenuByLocation");
  }

  /**
   * Cập nhật menu
   */
  async updateMenu(
    id: string,
    data: UpdateMenuData,
    updatedBy: UserEntity
  ): Promise<MenuEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ValidationError("Only admins can update menus");
      }

      // Check if menu exists
      const existingMenu = await this.menuRepository.findById(id);
      if (!existingMenu) {
        throw new NotFoundError("Menu", id);
      }

      // Validate location if provided
      if (data.location && data.location !== existingMenu.location) {
        const menuWithLocation = await this.menuRepository.findByLocation(
          data.location
        );
        if (menuWithLocation) {
          throw new ConflictError(
            `Menu for location '${data.location}' already exists`
          );
        }
      }

      // Update menu
      const updatedMenu = (await this.menuRepository.update(
        id,
        data
      )) as unknown as MenuEntity;

      // Log activity
      await this.logActivity("MENU_UPDATED", updatedBy.id, {
        menuId: id,
        changes: data,
      });

      return updatedMenu;
    }, "updateMenu");
  }

  /**
   * Xóa menu
   */
  async deleteMenu(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(deletedBy)) {
        throw new ValidationError("Only admins can delete menus");
      }

      // Check if menu exists
      const menu = await this.menuRepository.findById(id);
      if (!menu) {
        throw new NotFoundError("Menu", id);
      }

      // Get menu item count (assuming 0 for now since countMenuItems doesn't exist)
      const menuItemCount = 0; // TODO: Implement countMenuItems in repository

      // Check if menu can be deleted
      const canDeleteResult = MenuBusinessRules.canDelete(
        menu as unknown as MenuEntity,
        menuItemCount
      );
      if (!canDeleteResult.canDelete) {
        throw new ValidationError(
          canDeleteResult.reason || "Cannot delete menu"
        );
      }

      // Delete menu
      await this.menuRepository.delete(id);

      // Log activity
      await this.logActivity("MENU_DELETED", deletedBy.id, {
        menuId: id,
        menuName: menu.name,
      });
    }, "deleteMenu");
  }

  /**
   * Lấy danh sách menus
   */
  async getMenus(
    filters: SearchFilters = {}
  ): Promise<PaginatedResult<MenuEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = {};

      if (filters.search) {
        const searchQuery = this.sanitizeSearchQuery(filters.search);
        searchConditions.OR = [
          { name: { contains: searchQuery, mode: "insensitive" } },
          { location: { contains: searchQuery, mode: "insensitive" } },
        ];
      }

      if (filters.status) {
        searchConditions.status = filters.status;
      }

      // Get menus with pagination
      const result = await this.menuRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || "name"]: filters.sortOrder || "asc",
        },
      });

      return {
        data: result.data.map((menu) => menu as unknown as MenuEntity),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      };
    }, "getMenus");
  }

  /**
   * Lấy menu tree với items
   */
  async getMenuTree(location: string): Promise<MenuTreeNode[]> {
    return this.executeWithErrorHandling(async () => {
      const menu = await this.menuRepository.findByLocation(location);
      if (!menu) {
        throw new NotFoundError("Menu", location);
      }

      const menuTree = await this.menuRepository.findMany({
        where: { parentId: null },
        include: { children: true },
      });
      return menuTree.map((item) => item as unknown as MenuTreeNode);
    }, "getMenuTree");
  }

  /**
   * Lấy active menus
   */
  async getActiveMenus(): Promise<MenuEntity[]> {
    return this.executeWithErrorHandling(async () => {
      const menus = await this.menuRepository.getActiveMenus();
      return menus.map((menu) => menu as unknown as MenuEntity);
    }, "getActiveMenus");
  }

  /**
   * Cập nhật trạng thái menu
   */
  async updateMenuStatus(
    id: string,
    status: "ACTIVE" | "INACTIVE",
    updatedBy: UserEntity
  ): Promise<MenuEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ValidationError("Only admins can update menu status");
      }

      const menu = await this.updateMenu(
        id,
        { isActive: status === "ACTIVE" },
        updatedBy
      );

      // Log activity
      await this.logActivity("MENU_STATUS_UPDATED", updatedBy.id, {
        menuId: id,
        newStatus: status,
      });

      return menu;
    }, "updateMenuStatus");
  }

  /**
   * Duplicate menu
   */
  async duplicateMenu(
    id: string,
    newLocation: string,
    newName?: string,
    createdBy?: UserEntity
  ): Promise<MenuEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (createdBy && !this.isAdmin(createdBy)) {
        throw new ValidationError("Only admins can duplicate menus");
      }

      // Check if source menu exists
      const sourceMenu = await this.menuRepository.findById(id);
      if (!sourceMenu) {
        throw new NotFoundError("Menu", id);
      }

      // Check if new location is available
      const existingMenu =
        await this.menuRepository.findByLocation(newLocation);
      if (existingMenu) {
        throw new ConflictError(
          `Menu for location '${newLocation}' already exists`
        );
      }

      // Create new menu
      const newMenuData = {
        name: newName || `${sourceMenu.name} (Copy)`,
        location: newLocation,
        description: sourceMenu.description,
        status: "INACTIVE", // Start as inactive
      };

      const newMenu = (await this.menuRepository.create(
        newMenuData
      )) as unknown as MenuEntity;

      // TODO: Copy menu items
      // await this.duplicateMenuItems(id, newMenu.id);

      // Log activity
      if (createdBy) {
        await this.logActivity("MENU_DUPLICATED", createdBy.id, {
          sourceMenuId: id,
          newMenuId: newMenu.id,
          newLocation,
        });
      }

      return newMenu;
    }, "duplicateMenu");
  }

  /**
   * Reorder menus
   */
  async reorderMenus(
    menuOrders: { id: string; order: number }[],
    updatedBy: UserEntity
  ): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ValidationError("Only admins can reorder menus");
      }

      // Update menu orders
      await Promise.all(
        menuOrders.map(({ id, order }) =>
          this.menuRepository.update(id, { sortOrder: order } as any)
        )
      );

      // Log activity
      await this.logActivity("MENUS_REORDERED", updatedBy.id, {
        menuCount: menuOrders.length,
      });
    }, "reorderMenus");
  }

  /**
   * Get menu statistics
   */
  async getMenuStats(id: string): Promise<{
    totalItems: number;
    activeItems: number;
    maxDepth: number;
  }> {
    return this.executeWithErrorHandling(async () => {
      const menu = await this.menuRepository.findById(id);
      if (!menu) {
        throw new NotFoundError("Menu", id);
      }

      const [totalItems, activeItems] = await Promise.all([
        this.menuRepository.count({ where: { menuId: id } }),
        this.menuRepository.count({ where: { menuId: id, isActive: true } }),
      ]);

      // TODO: Calculate max depth
      const maxDepth = 0;

      return {
        totalItems,
        activeItems,
        maxDepth,
      };
    }, "getMenuStats");
  }

  /**
   * Search menus
   */
  async searchMenus(
    query: string,
    filters: SearchFilters = {}
  ): Promise<PaginatedResult<MenuEntity>> {
    return this.executeWithErrorHandling(async () => {
      const searchFilters = {
        ...filters,
        search: query,
      };
      return await this.getMenus(searchFilters);
    }, "searchMenus");
  }

  /**
   * Helper method to check if user is admin
   */
  private isAdmin(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN"].includes(user.role);
  }
}
