import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/lib/admin/auth";
import { container } from "@/app/api/di-container";
import { USER_SERVICE } from "@/app/api/services/service-identifiers";
import { ForbiddenError } from "@/app/models/common.model";
import { UserService } from "@/app/api/services/user.service";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";
    const gender = searchParams.get("gender") || "";

    // Get UserService from DI container
    const userService = container.resolve<UserService>(USER_SERVICE);

    // Build search filters
    const filters: any = {};

    if (search) {
      filters.search = search;
    }

    if (gender && gender !== "all") {
      filters.gender = gender;
    }

    // Search users using service
    const result = await userService.searchUsers(filters, page, limit);

    // Calculate stats for each customer
    const customersWithStats = result.data.map((customer: any) => {
      // Remove sensitive data
      const { password: _password, ...customerData } = customer;

      return {
        ...customerData,
        totalOrders: customer._count?.orders || 0,
        totalReviews: customer._count?.reviews || 0,
        totalAddresses: customer._count?.addresses || 0,
      };
    });

    return NextResponse.json({
      success: true,
      data: customersWithStats,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  } catch (error) {
    if (error instanceof ForbiddenError) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    console.error("Get customers error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách khách hàng" },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const customerId = searchParams.get("id");

    if (!customerId) {
      return NextResponse.json(
        { error: "Customer ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { isActive } = body;

    // Update customer status
    const updatedCustomer = await prisma.user.update({
      where: { id: customerId },
      data: { isActive },
      include: {
        _count: {
          select: {
            orders: true,
            reviews: true,
            addresses: true,
          },
        },
      },
    });

    return NextResponse.json({
      data: updatedCustomer,
      message: isActive
        ? "Customer activated successfully"
        : "Customer deactivated successfully",
    });
  } catch (error) {
    console.error("Error updating customer:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
