/**
 * Review Model
 * Business entity cho Review
 */

import { BaseEntity, Status } from "./common.model";

/**
 * Review Entity
 */
export interface ReviewEntity extends BaseEntity {
  userId: string;
  productId: string;
  orderId?: string;
  rating: number;
  title?: string;
  comment: string;
  status: ReviewStatus;
  isVerifiedPurchase: boolean;
  helpfulCount: number;
  reportCount: number;
  metadata?: Record<string, any>;
}

/**
 * Review Status
 */
export enum ReviewStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
  HIDDEN = "HIDDEN",
}

/**
 * Review with Relations
 */
export interface ReviewWithRelations extends ReviewEntity {
  user?: any; // UserEntity
  product?: any; // ProductEntity
  order?: any; // OrderEntity
}

/**
 * Create Review Data
 */
export interface CreateReviewData {
  userId: string;
  productId: string;
  orderId?: string;
  rating: number;
  title?: string;
  comment: string;
  isVerifiedPurchase?: boolean;
}

/**
 * Update Review Data
 */
export interface UpdateReviewData {
  rating?: number;
  title?: string;
  comment?: string;
  status?: ReviewStatus;
}

/**
 * Review Statistics
 */
export interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

/**
 * Review Business Rules
 */
export class ReviewBusinessRules {
  static validateRating(rating: number): boolean {
    return rating >= 1 && rating <= 5 && Number.isInteger(rating);
  }

  static validateComment(comment: string): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!comment || comment.trim().length === 0) {
      errors.push("Comment is required");
    }

    if (comment.length < 10) {
      errors.push("Comment must be at least 10 characters");
    }

    if (comment.length > 1000) {
      errors.push("Comment must be less than 1000 characters");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static canUserReview(
    userId: string,
    productId: string,
    existingReviews: ReviewEntity[]
  ): boolean {
    // Check if user already reviewed this product
    return !existingReviews.some(
      (review) => review.userId === userId && review.productId === productId
    );
  }

  static calculateAverageRating(reviews: ReviewEntity[]): number {
    if (reviews.length === 0) return 0;

    const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
    return Math.round((sum / reviews.length) * 10) / 10;
  }

  static validateReview(data: CreateReviewData | UpdateReviewData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate rating
    if ("rating" in data && data.rating !== undefined) {
      if (!this.validateRating(data.rating)) {
        errors.push("Rating must be an integer between 1 and 5");
      }
    }

    // Validate comment
    if ("comment" in data && data.comment !== undefined) {
      const commentValidation = this.validateComment(data.comment);
      if (!commentValidation.valid) {
        errors.push(...commentValidation.errors);
      }
    }

    // Validate userId (for create)
    if ("userId" in data && !data.userId?.trim()) {
      errors.push("User ID is required");
    }

    // Validate productId (for create)
    if ("productId" in data && !data.productId?.trim()) {
      errors.push("Product ID is required");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static canUpdate(
    review: ReviewEntity,
    userId: string
  ): {
    canUpdate: boolean;
    reason?: string;
  } {
    // Only the review author can update their review
    if (review.userId !== userId) {
      return {
        canUpdate: false,
        reason: "Only the review author can update this review",
      };
    }

    // Check if review is too old to update (e.g., 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    if (review.createdAt < thirtyDaysAgo) {
      return {
        canUpdate: false,
        reason: "Review is too old to update (30 days limit)",
      };
    }

    return { canUpdate: true };
  }

  static canDelete(
    review: ReviewEntity,
    userId: string,
    isAdmin: boolean = false
  ): {
    canDelete: boolean;
    reason?: string;
  } {
    // Admins can delete any review
    if (isAdmin) {
      return { canDelete: true };
    }

    // Only the review author can delete their review
    if (review.userId !== userId) {
      return {
        canDelete: false,
        reason: "Only the review author can delete this review",
      };
    }

    return { canDelete: true };
  }

  static getStatusLabel(status: ReviewStatus): string {
    const statusLabels: Record<ReviewStatus, string> = {
      [ReviewStatus.PENDING]: "Pending Review",
      [ReviewStatus.APPROVED]: "Approved",
      [ReviewStatus.REJECTED]: "Rejected",
      [ReviewStatus.HIDDEN]: "Hidden",
    };

    return statusLabels[status] || status;
  }

  static isHelpful(helpfulCount: number, totalVotes: number): boolean {
    if (totalVotes === 0) return false;
    return helpfulCount / totalVotes >= 0.6; // 60% helpful threshold
  }

  static shouldAutoApprove(rating: number, comment: string): boolean {
    // Auto-approve positive reviews with reasonable comments
    if (rating >= 4 && comment.length >= 20 && comment.length <= 500) {
      // Check for spam indicators
      const spamWords = ["spam", "fake", "bot", "promotional"];
      const hasSpam = spamWords.some((word) =>
        comment.toLowerCase().includes(word)
      );

      return !hasSpam;
    }

    return false;
  }

  static getRatingDistribution(
    reviews: ReviewEntity[]
  ): Record<number, number> {
    const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };

    reviews.forEach((review) => {
      if (review.rating >= 1 && review.rating <= 5) {
        distribution[review.rating as keyof typeof distribution]++;
      }
    });

    return distribution;
  }

  static getReviewSummary(reviews: ReviewEntity[]): {
    totalReviews: number;
    averageRating: number;
    distribution: Record<number, number>;
    recommendationPercentage: number;
  } {
    const totalReviews = reviews.length;
    const averageRating = this.calculateAverageRating(reviews);
    const distribution = this.getRatingDistribution(reviews);

    // Calculate recommendation percentage (4-5 star reviews)
    const positiveReviews = reviews.filter((r) => r.rating >= 4).length;
    const recommendationPercentage =
      totalReviews > 0 ? Math.round((positiveReviews / totalReviews) * 100) : 0;

    return {
      totalReviews,
      averageRating,
      distribution,
      recommendationPercentage,
    };
  }

  static filterProfanity(comment: string): string {
    // Simple profanity filter - in production, use a proper library
    const profanityWords = ["damn", "hell", "crap"]; // Add more as needed
    let filtered = comment;

    profanityWords.forEach((word) => {
      const regex = new RegExp(word, "gi");
      filtered = filtered.replace(regex, "*".repeat(word.length));
    });

    return filtered;
  }

  static generateReviewExcerpt(
    comment: string,
    maxLength: number = 100
  ): string {
    if (comment.length <= maxLength) return comment;

    const truncated = comment.substring(0, maxLength);
    const lastSpace = truncated.lastIndexOf(" ");

    return lastSpace > 0
      ? truncated.substring(0, lastSpace) + "..."
      : truncated + "...";
  }
}
